<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Language Test - Home</title>
  <link rel="stylesheet" href="dis_crossculture.css">

  <style>
    
    body,
    html {
      margin: 0;
      padding: 0;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: #f5f5f5;
      /* 浅灰色背景 */
      color: #333333;
      /* 深灰色文本 */
      text-align: center;
    }

    .header {
      padding: 40px 20px;
      background-color: #76323F;
      color: white;
    }

    .header h1 {
      font-size: 2.5em;
      margin-bottom: 0.5em;
    }

    .header p {
      font-size: 1.2em;
    }

    /* 媒体查询 */
    @media (max-width: 768px) {
      .container {
        padding: 10%;
      }

      .header h1 {
        font-size: 2em;
      }

      .header p {
        font-size: 1em;
      }

      .btn {
        font-size: 1em;
        padding: 8px 16px;
        margin: 15px;
      }

      .question {
        line-height: 1.4;
      }
    }

    @media (max-width: 480px) {
      .container {
        padding: 10%;
      }

      .header h1 {
        font-size: 1.5em;
      }

      .header p {
        font-size: 0.9em;
      }

      .btn {
        font-size: 0.9em;
        padding: 6px 12px;
        margin: 10px;
      }

      .question {
        line-height: 1.3;
      }
    }

    /* 取消无序列表的缩进和默认样式 */
    ul {
      padding-left: 2.5%; /* 移除默认的左边距 */
    }

    /* 取消有序列表的缩进和默认样式 */
    ol {
      padding-left: 2.5%; /* 移除默认的左边距 */
    }

  </style>
  
</head>

<body>




  <div class="container" style="width: 80%">


    <div class="question" style="text-align: left; line-height: 2">
      <div style="margin-bottom: 3%"><strong>Welcome to the experiment!</strong></div>
      <div>You will now see 8 images. For each image, please describe it in both Chinese and English, with the order of tasks randomized.</div>

      <div style="padding-top:2%"><strong>Instructions：</strong></div>
      <div>We would like you to <strong>narrate a complete story</strong> based on the image, including:</div>

      <ol>
        <li>What might have happened before?</li>
        <li>What is happening now?</li>
        <li>What could happen next?</li>
        <li>What are the characters thinking or feeling? Why?</li>
      </ol>

      <div>
        <div style="padding-top:3%; padding-bottom: 3%"><strong>Before each description, please organize your answer at least 30 seconds. After countdown, audio recording will be allowed to start.
          Each description should last at least 2 minutes.</strong></div>
        <div><strong>Note: </strong></div>
        <ul>
          <li>Please do not use any reference tools.</li>
          <li>Make sure you complete all language tasks without interruption.</li>
        </ul>
      <p>We look forward to your outstanding performance!</p>
      </div>

    </div>

    <button class="btn" onclick="location.href='TATTask_cross_culture_en.html?idNumber='+ encodeURIComponent(idNumber)">
      Start Task
    </button>
  </div>

  <!-- <div class="footer">
  &copy; 2024 Language test
</div> -->
  <script>
    //var idNumber =location.search.replace(/[^\d]/g,"");
    var idNumber = decodeURIComponent(location.search.match(/[^\?&]*idNumber=([^&]*)/)[1]);

  </script>
</body>

</html>