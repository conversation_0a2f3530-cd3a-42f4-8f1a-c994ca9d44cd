#!/usr/bin/env python3
"""
HTTP Server for Audio File Uploads
Receives audio data from vft_seq_cross_culture_en.html and saves as WAV files
"""

import os
import sys
import logging
import tempfile
from datetime import datetime
from flask import Flask, request, jsonify, render_template_string, send_from_directory
from werkzeug.utils import secure_filename
import wave
import io
import struct 

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('server.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# Configuration
UPLOAD_FOLDER = 'data'
STATIC_FOLDER = 'static'
ALLOWED_EXTENSIONS = {'wav', 'webm', 'ogg', 'mp3'}
PORT = 8080

# Ensure upload folder exists
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

def allowed_file(filename):
    """Check if the file has an allowed extension"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def convert_to_wav(audio_data, original_format):
    """
    Convert audio data to WAV format
    Uses pydub for conversion if available, otherwise saves as-is with .wav extension
    """
    try:
        # Try to use pydub for conversion if available
        from pydub import AudioSegment
        
        # Create a temporary file with the original data
        with tempfile.NamedTemporaryFile(suffix=f'.{original_format}', delete=False) as temp_file:
            temp_file.write(audio_data)
            temp_file_path = temp_file.name
        
        try:
            # Load the audio file
            if original_format.lower() == 'webm':
                audio = AudioSegment.from_file(temp_file_path, format='webm')
            elif original_format.lower() == 'ogg':
                audio = AudioSegment.from_file(temp_file_path, format='ogg')
            elif original_format.lower() == 'mp3':
                audio = AudioSegment.from_file(temp_file_path, format='mp3')
            else:
                # If format is not recognized, try to auto-detect
                audio = AudioSegment.from_file(temp_file_path)
            
            # Export as WAV
            wav_data = io.BytesIO()
            audio.export(wav_data, format='wav')
            wav_data.seek(0)
            return wav_data.read()
            
        finally:
            # Clean up the temporary file
            os.unlink(temp_file_path)
            
    except ImportError:
        # pydub not available, just return the original data
        logger.warning("pydub not available, saving original data with .wav extension")
        return audio_data
    except Exception as e:
        logger.error(f"Error converting audio: {str(e)}")
        # If conversion fails, return the original data
        return audio_data

@app.route('/')
def index():
    """Serve the Welcome_cross_culture_en.html file"""
    try:
        return send_from_directory(STATIC_FOLDER, 'Welcome_cross_culture_en.html')
    except Exception as e:
        logger.error(f"Error serving Welcome_cross_culture_en.html: {str(e)}")
        return jsonify({'error': 'Welcome page not found'}), 404



@app.route('/upload', methods=['POST'])
def upload_file():
    """Handle audio file upload"""
    try:
        logger.info('Received upload request')
        
        # Check if the post request has the file part
        if 'file' not in request.files:
            logger.error('No file part in request')
            return jsonify({'error': 'No file part'}), 400
        
        file = request.files['file']
        logger.info(f'File received: {file.filename}, Content-Type: {file.content_type}')
        
        # Check if file is selected
        if file.filename == '':
            logger.error('No file selected')
            return jsonify({'error': 'No file selected'}), 400
        
        # Check file extension
        if not allowed_file(file.filename):
            logger.error(f'File type not allowed: {file.filename}')
            return jsonify({'error': 'File type not allowed'}), 400
        
        # Get metadata from form
        id_number = request.form.get('idNumber', 'unknown')
        task_id = request.form.get('taskId', 'unknown')
        timestamp = request.form.get('timestamp', datetime.now().strftime('%Y%m%d%H%M%S'))
        
        logger.info(f'Metadata - ID: {id_number}, Task: {task_id}, Timestamp: {timestamp}')
        
        # Create secure filename with metadata
        original_filename = secure_filename(file.filename)
        file_extension = original_filename.rsplit('.', 1)[1].lower()
        new_filename = f"{id_number}_vft{task_id}_{timestamp}.wav"
        
        logger.info(f'Original filename: {original_filename}, New filename: {new_filename}')
        
        # Read file data
        file_data = file.read()
        original_size = len(file_data)
        logger.info(f'Original file size: {original_size} bytes')
        
        # Convert to WAV if needed
        if file_extension != 'wav':
            logger.info(f'Converting {file_extension} to WAV')
            file_data = convert_to_wav(file_data, file_extension)
            converted_size = len(file_data)
            logger.info(f'Converted file size: {converted_size} bytes')
        
        # Save file
        file_path = os.path.join(UPLOAD_FOLDER, new_filename)
        with open(file_path, 'wb') as f:
            f.write(file_data)
        
        file_size = len(file_data)
        logger.info(f'File saved: {new_filename} ({file_size} bytes) at {file_path}')
        
        return jsonify({
            'message': 'File uploaded successfully',
            'filename': new_filename,
            'size': file_size,
            'path': file_path
        }), 200
        
    except Exception as e:
        logger.error(f'Error uploading file: {str(e)}', exc_info=True)
        return jsonify({'error': f'Upload failed: {str(e)}'}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({'status': 'healthy', 'timestamp': datetime.now().isoformat()}), 200

@app.route('//<path:filename>')
def serve_static_files(filename):
    """Serve static files from the static directory"""
    try:
        return send_from_directory(STATIC_FOLDER, filename)
    except Exception as e:
        logger.error(f"Error serving static file {filename}: {str(e)}")
        return jsonify({'error': 'File not found'}), 404




def main():
    """Main function to start the server"""
    try:
        logger.info(f"Starting audio upload server on port {PORT}")
        logger.info(f"Upload folder: {os.path.abspath(UPLOAD_FOLDER)}")
        logger.info(f"Static folder: {os.path.abspath(STATIC_FOLDER)}")
        logger.info("Press Ctrl+C to stop the server")
        
        # Start server
        app.run(host='0.0.0.0', port=PORT, debug=False, threaded=True)
        
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()