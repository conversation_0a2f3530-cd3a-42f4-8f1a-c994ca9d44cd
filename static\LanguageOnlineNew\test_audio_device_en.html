<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <title>Discourse</title>
    <link rel="stylesheet" href="dis_crossculture.css">

</head>

<body>
    <div class="container">
        <h1 style="font-weight: bold; text-align: center;">Audio Recording Check</h1>

        <p class="question">Please speak for about 5 seconds, ensuring your voice is clear. You may stop recording once you are satisfied.</p>
        <p class="question">Click the play button on the bar below to listen to your recording and ensure the sound quality is clear and smooth.</p>

        <button id="startRecord" class="btn">Start</button>
        <button id="stopRecord" class="btn" disabled>Stop</button>
        <audio id="audio" controls class="audio-player"></audio>
        <p id="status" class="status-text">Awaiting recording</p>

        <button id="nextQuestion" class="btn" disabled>Next</button>
    </div>

    <script>
        //var idNumber = 'yjhtest';
        var idNumber = decodeURIComponent(location.search.match(/[^\?&]*idNumber=([^&]*)/)[1]);
        

        let startRecord = document.getElementById('startRecord');
        let stopRecord = document.getElementById('stopRecord');
        let audio = document.getElementById('audio');
        let statusText = document.getElementById('status');
        let nextQuestion = document.getElementById('nextQuestion');

        let mediaRecorder;
        let audioChunks = [];
        let recordingDuration = 0;
        let recordingInterval;



        document.addEventListener('DOMContentLoaded', function () {

            startRecord.onclick = function () {

                audio.style.display = 'none';
                audioChunks = [];
                audio.src = '';
                navigator.mediaDevices.getUserMedia({ audio: true })
                    .then(stream => {
                        let options = {};
                        if (MediaRecorder.isTypeSupported('audio/webm')) {
                            options.mimeType = 'audio/webm';
                        } else {
                            console.log('Using browser default audio format.');
                        }
                        mediaRecorder = new MediaRecorder(stream, options);
                        mediaRecorder.ondataavailable = function (e) {
                            audioChunks.push(e.data);
                        };
                        mediaRecorder.onstop = function () {
                            let audioBlob = new Blob(audioChunks, { type: mediaRecorder.mimeType });
                            let audioUrl = URL.createObjectURL(audioBlob);
                            audio.src = audioUrl;
                            clearInterval(recordingInterval);

                        };
                        mediaRecorder.start();
                        recordingDuration = 0;
                        recordingInterval = setInterval(function () {
                            recordingDuration++;
                            statusText.textContent = "Recording ｜ Time：" + recordingDuration + " sec";
                        }, 1000);
                        setTimeout(function () {
                            stopRecord.disabled = false;
                        }, 5*1000);

                        startRecord.disabled = true;

                        startRecord.textContent = "Rerecord";
                        statusText.textContent = "Recording started";
                    });
            };



            stopRecord.onclick = function () {

                mediaRecorder.stop();
                startRecord.disabled = false;
                stopRecord.disabled = true;

                nextQuestion.disabled = false;


                statusText.textContent = "Recording has stopped.";
                //显示录音进度条
                audio.style.display = 'block';
            };

            nextQuestion.onclick = function () {
                const url = new URL(window.location);
                const params = new URLSearchParams(url.search);
                const task = params.get('task');

                if(task=='TAT'){
                    window.location.href = 'dis_seq_cross_culture_en.html?idNumber=' + encodeURIComponent(idNumber);
                }else if(task=='VFT'){
                    window.location.href='cross_culture_vft_intro_en.html?idNumber='+ encodeURIComponent(idNumber);
                }else{
                    console.log(`${task} unknown!`)
                }

            }

        });
    </script>


</body>

</html>