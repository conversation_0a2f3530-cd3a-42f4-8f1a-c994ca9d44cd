@echo off
echo Building standalone executable for Audio Upload Server...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed or not in PATH
    echo Please install Python to build the executable
    pause
    exit /b 1
)

REM Run the build script
python build_exe.py

echo.
echo Build process completed.
echo.
echo If successful, you can find the executable in the 'dist' folder
pause