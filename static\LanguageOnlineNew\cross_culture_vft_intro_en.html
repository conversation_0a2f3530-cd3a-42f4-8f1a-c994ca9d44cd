<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <title>Language Test - Home</title>
    <link href="dis_crossculture.css" rel="stylesheet">
    <style>
        body,
        html {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            /* 浅灰色背景 */
            color: #333333;
            /* 深灰色文本 */
            text-align: center;
        }

        .header {
            padding: 40px 20px;
            background-color: #76323F;
            color: white;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 0.5em;
        }

        .header p {
            font-size: 1.2em;
        }


        #button:hover {
            background-color: #76323F;
            /* 设置鼠标悬停时的按钮背景颜色 */
        }

        /* 媒体查询 */
        @media (max-width: 768px) {
            .container {
                padding: 10%;
            }

            .header h1 {
                font-size: 2em;
            }

            .header p {
                font-size: 1em;
            }

            .btn {
                font-size: 1em;
                padding: 8px 16px;
                margin: 15px;
            }

            .question {
                line-height: 1.4;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 10%;
            }

            .header h1 {
                font-size: 1.5em;
            }

            .header p {
                font-size: 0.9em;
            }

            .btn {
                font-size: 0.9em;
                padding: 6px 12px;
                margin: 10px;
            }

            .question {
                line-height: 1.3;
            }
        }
        /* 取消无序列表的缩进和默认样式 */
        ul {
            padding-left: 3%; /* 移除默认的左边距 */
        }

        /* 取消有序列表的缩进和默认样式 */
        ol {
            padding-left: 3%; /* 移除默认的左边距 */
        }
    </style>
    
</head>

<body>


    <div class="container" style="width: 80%">


        <div class="question" style="text-align: left; line-height: 2">
            <div style="margin-bottom: 3%"><strong>Welcome to the experiment!</strong></div>

            <div>In the next 30 minutes, you'll complete a series of language tasks in both Chinese and English. The tasks will be presented one by one in random order.</div>

            <div>
                <div style="padding-top:2%"><strong>Instructions:</strong></div>
                <ol>
                    <li>When ready, click <em>Start recording</em>.</li>
                    <li>You’ll have 5 minutes for each task.</li>
                    <li>Once finished, click <em>Stop recording</em> and then <em>Next question</em> to move on.</li>
                </ol>
            </div>


            <div>
                <strong>Note:</strong>
                <ul>
                    <li>Do not use any reference tools.</li>
                    <li>Complete all tasks without interruption.</li>
                </ul>
            </div>
            We look forward to your excellent performance!
        </div>

        <button class="btn"
            onclick="location.href='vft_seq_cross_culture_en.html?idNumber='+ encodeURIComponent(idNumber)">
            Start Task
        </button>
    </div>

    <!-- <div class="footer">
  &copy; 2024 Language test
</div> -->
    <script>
        //var idNumber =location.search.replace(/[^\d]/g,"");
        var idNumber = decodeURIComponent(location.search.match(/[^\?&]*idNumber=([^&]*)/)[1]);

    </script>
</body>

</html>