# Audio Upload Server

This is a portable HTTP server designed to receive audio data from the `vft_seq_cross_culture_en.html` file and save it as WAV files in the `data` directory.

## Features

- Receives audio data from the web interface
- Saves audio files in WAV format
- Portable - can be run with a double-click
- No external dependencies required
- Includes fallback to localStorage if server is unavailable

## Files

- `server.py` - Python server script
- `run_server.bat` - Batch file to run the server with Python
- `build_exe.py` - Python script to build the executable
- `build_exe.bat` - Batch file to build the executable
- `requirements.txt` - Python dependencies
- `data/` - Directory where audio files are stored
- `static/LanguageOnlineNew/vft_seq_cross_culture_en.html` - Modified HTML file that sends audio to the server

## Quick Start

### Option 1: Using the Executable (Recommended)

1. Build the executable:
   - Double-click `build_exe.bat`
   - Wait for the build process to complete
   - The executable will be created in the `dist` folder

2. Run the server:
   - Double-click `dist/server.exe`
   - The server will start on port 8080

3. Use the web interface:
   - Open `static/LanguageOnlineNew/vft_seq_cross_culture_en.html` in a web browser
   - Record audio as usual
   - The audio will be automatically uploaded to the server and saved in the `data` directory

### Option 2: Using Python

1. Ensure Python is installed on your system

2. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

3. Run the server:
   - Double-click `run_server.bat`
   - Or run `python server.py` from the command line

4. Use the web interface as described above

## Server Status

You can check the server status by visiting `http://localhost:8080` in your web browser. The status page shows:
- Server status
- Port number
- Upload folder path
- List of recently uploaded files

## Audio File Storage

Audio files are saved in the `data` directory with the following naming convention:
```
{idNumber}_vft{taskId}_{timestamp}.wav
```

For example:
```
user123_vft1_1703020800.wav
```

## Troubleshooting

### Server won't start

- If using the executable: Ensure you have built it correctly using `build_exe.bat`
- If using Python: Ensure Python is installed and all dependencies are installed

### Audio files not being saved

- Check that the server is running by visiting `http://localhost:8080`
- Check the server console for error messages
- Ensure the `data` directory exists and is writable

### Upload fails

- The HTML file includes fallback to localStorage if the server is unavailable
- Check browser console for error messages
- Ensure the server is running on port 8080

## Building the Executable

To build the standalone executable:

1. Ensure Python is installed
2. Double-click `build_exe.bat`
3. Wait for the build process to complete
4. The executable will be created in the `dist` folder

The executable includes all necessary dependencies and can be run on any Windows computer without installing Python.

## Technical Details

- Server runs on port 8080
- Accepts POST requests at `/upload`
- Handles multipart/form-data for file uploads
- Converts audio files to WAV format
- Logs server activity to `server.log`

## Security Notes

- The server is designed for local use only
- It binds to all network interfaces (0.0.0.0)
- Consider firewall settings if using on a network
- No authentication is implemented - use in trusted environments only