@echo off
echo Starting Audio Upload Server...
echo.
echo The server will run on port 8080
echo.
echo You can access the server status page at: http://localhost:8080
echo.
echo Press Ctrl+C to stop the server
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed or not in PATH
    echo Please install Python or use the server.exe file
    pause
    exit /b 1
)

REM Run the server
python server.py

pause