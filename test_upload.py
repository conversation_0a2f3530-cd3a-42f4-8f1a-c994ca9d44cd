#!/usr/bin/env python3
"""
Test script to verify the server's file upload functionality
"""

import requests
import json
from datetime import datetime

def test_server():
    """Test the server's health and upload functionality"""
    
    # Test health endpoint
    print("Testing health endpoint...")
    try:
        response = requests.get('http://localhost:8080/health')
        if response.status_code == 200:
            print("[PASS] Health check passed")
            print(f"  Response: {response.json()}")
        else:
            print(f"[FAIL] Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"[FAIL] Health check failed: {e}")
        return False
    
    # Test file upload
    print("\nTesting file upload...")
    try:
        # Create a dummy audio file (just a text file for testing)
        test_data = b"This is a test audio file"
        timestamp = int(datetime.now().timestamp())
        
        files = {
            'file': (f'test_vft1_{timestamp}.wav', test_data, 'audio/wav')
        }
        
        data = {
            'idNumber': 'testuser',
            'taskId': '1',
            'timestamp': str(timestamp)
        }
        
        response = requests.post('http://localhost:8080/upload', files=files, data=data)
        
        if response.status_code == 200:
            print("[PASS] File upload test passed")
            result = response.json()
            print(f"  Response: {json.dumps(result, indent=2)}")
            return True
        else:
            print(f"[FAIL] File upload test failed: {response.status_code}")
            print(f"  Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"[FAIL] File upload test failed: {e}")
        return False

if __name__ == '__main__':
    print("Testing Audio Upload Server...")
    print("=" * 40)
    
    success = test_server()
    
    print("\n" + "=" * 40)
    if success:
        print("[PASS] All tests passed! The server is working correctly.")
    else:
        print("[FAIL] Some tests failed. Please check the server logs.")